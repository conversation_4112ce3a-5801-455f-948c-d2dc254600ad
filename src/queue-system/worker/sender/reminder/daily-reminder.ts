import { Injectable, Module, OnApplicationBootstrap } from '@nestjs/common';
import { FcmService } from 'src/helper/fcmService';
import { QueueInstance } from 'src/queue-system';
import { NotificationQueueName } from 'src/queue-system/predefined-data';
import { FCMSenderPayload } from 'src/queue-system/types';

class FcmServiceHelper extends FcmService {}

@Injectable()
export class DailyReminderSenderQueue implements OnApplicationBootstrap {
  async onApplicationBootstrap() {
    try {
      await (
        await QueueInstance
      ).consume(NotificationQueueName.DAILY_REMINDER_SENDER_QUEUE);
    } catch (error) {
      console.log(error.message);
    }
  }

  static async sender(payload: FCMSenderPayload): Promise<void> {
    try {
      const { title, body, token, isHighPriority, data, documentId } = payload;

      console.log('📱 Daily Reminder Sender Queue received:', {
        title,
        body,
        token: token ? `${token.substring(0, 20)}...` : 'null',
        isHighPriority,
        documentId,
      });

      // Send FCM Push Notification
      if (token && body) {
        console.log('🚀 Sending FCM notification...');
        const result = await new FcmServiceHelper().sendToIndividual({
          token,
          title,
          body,
          documentId,
          data,
          isHighPriority,
          ttl: 600,
        });
        console.log('✅ FCM notification sent successfully:', result);
      } else {
        console.log('❌ Missing token or body for FCM notification');
      }
    } catch (error: any) {
      console.error('❌ Daily Reminder Sender Queue error:', error.message);
    }
  }
}

@Module({
  providers: [DailyReminderSenderQueue],
})
export class DailyReminderSenderModule {}
