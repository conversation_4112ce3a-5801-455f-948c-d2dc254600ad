import { Injectable, Module, OnApplicationBootstrap } from '@nestjs/common';
import { FcmService } from 'src/helper/fcmService';
import { QueueInstance } from 'src/queue-system';
import { NotificationQueueName } from 'src/queue-system/predefined-data';
import { FCMSenderPayload } from 'src/queue-system/types';

class FcmServiceHelper extends FcmService {}

@Injectable()
export class DailyReminderSenderQueue implements OnApplicationBootstrap {
  async onApplicationBootstrap() {
    try {
      await (
        await QueueInstance
      ).consume(NotificationQueueName.DAILY_REMINDER_SENDER_QUEUE);
    } catch (error) {
      console.log(error.message);
    }
  }

  static async sender(payload: FCMSenderPayload): Promise<void> {
    try {
      const { title, body, token, isHighPriority, data, documentId } = payload;

      // Send FCM Push Notification
      if (token && body) {
        await new FcmServiceHelper().sendToIndividual({
          token,
          title,
          body,
          documentId,
          data,
          isHighPriority,
          ttl: 600,
        });
      }
    } catch (error: any) {
      console.error('Daily Reminder Sender Queue error:', error.message);
    }
  }
}

@Module({
  providers: [DailyReminderSenderQueue],
})
export class DailyReminderSenderModule {}
