import { Injectable } from '@nestjs/common';

export interface NotificationBatchStats {
  batchId: string;
  startTime: Date;
  endTime?: Date;
  totalUsers: number;
  sentCount: number;
  skippedCount: number;
  failedCount: number;
  reminderTitle?: string;
  timezone?: string;
}

@Injectable()
export class NotificationStatsService {
  private activeBatches = new Map<string, NotificationBatchStats>();
  private completedBatches: NotificationBatchStats[] = [];
  private readonly MAX_COMPLETED_BATCHES = 50; // Keep last 50 batches for monitoring

  /**
   * Start tracking a new notification batch
   */
  startBatch(batchId: string, totalUsers: number, reminderTitle?: string, timezone?: string): void {
    const batch: NotificationBatchStats = {
      batchId,
      startTime: new Date(),
      totalUsers,
      sentCount: 0,
      skippedCount: 0,
      failedCount: 0,
      reminderTitle,
      timezone,
    };

    this.activeBatches.set(batchId, batch);
  }

  /**
   * Increment sent count for a batch
   */
  incrementSent(batchId: string): void {
    const batch = this.activeBatches.get(batchId);
    if (batch) {
      batch.sentCount++;
    }
  }

  /**
   * Increment skipped count for a batch
   */
  incrementSkipped(batchId: string): void {
    const batch = this.activeBatches.get(batchId);
    if (batch) {
      batch.skippedCount++;
    }
  }

  /**
   * Increment failed count for a batch
   */
  incrementFailed(batchId: string): void {
    const batch = this.activeBatches.get(batchId);
    if (batch) {
      batch.failedCount++;
    }
  }

  /**
   * Complete a batch and log the aggregate statistics
   */
  completeBatch(batchId: string): void {
    const batch = this.activeBatches.get(batchId);
    if (!batch) {
      return;
    }

    batch.endTime = new Date();
    const duration = batch.endTime.getTime() - batch.startTime.getTime();
    const successRate = batch.totalUsers > 0 ? Math.round((batch.sentCount / batch.totalUsers) * 100) : 0;

    // Format the time for display
    const timeStr = batch.startTime.toLocaleTimeString('en-US', { 
      hour12: false, 
      hour: '2-digit', 
      minute: '2-digit' 
    });

    // Log the aggregate statistics in the requested format
    console.log(
      `📊 ${batch.timezone || 'UTC'} ${timeStr}: ${batch.sentCount} sent, ${batch.skippedCount} skipped, ${batch.failedCount} failed (${successRate}% success, ${duration}ms)`
    );

    // Move to completed batches
    this.activeBatches.delete(batchId);
    this.completedBatches.push(batch);

    // Keep only the last MAX_COMPLETED_BATCHES
    if (this.completedBatches.length > this.MAX_COMPLETED_BATCHES) {
      this.completedBatches.shift();
    }
  }

  /**
   * Get current batch statistics
   */
  getBatchStats(batchId: string): NotificationBatchStats | undefined {
    return this.activeBatches.get(batchId);
  }

  /**
   * Get all active batches
   */
  getActiveBatches(): NotificationBatchStats[] {
    return Array.from(this.activeBatches.values());
  }

  /**
   * Get completed batches
   */
  getCompletedBatches(): NotificationBatchStats[] {
    return [...this.completedBatches];
  }

  /**
   * Get overall statistics
   */
  getOverallStats(): {
    activeBatches: number;
    totalCompleted: number;
    last24Hours: {
      batches: number;
      totalSent: number;
      totalSkipped: number;
      totalFailed: number;
      averageSuccessRate: number;
    };
  } {
    const now = new Date();
    const last24Hours = new Date(now.getTime() - 24 * 60 * 60 * 1000);

    const recentBatches = this.completedBatches.filter(
      batch => batch.endTime && batch.endTime >= last24Hours
    );

    const totalSent = recentBatches.reduce((sum, batch) => sum + batch.sentCount, 0);
    const totalSkipped = recentBatches.reduce((sum, batch) => sum + batch.skippedCount, 0);
    const totalFailed = recentBatches.reduce((sum, batch) => sum + batch.failedCount, 0);
    const totalUsers = recentBatches.reduce((sum, batch) => sum + batch.totalUsers, 0);
    
    const averageSuccessRate = totalUsers > 0 ? (totalSent / totalUsers) * 100 : 0;

    return {
      activeBatches: this.activeBatches.size,
      totalCompleted: this.completedBatches.length,
      last24Hours: {
        batches: recentBatches.length,
        totalSent,
        totalSkipped,
        totalFailed,
        averageSuccessRate: Math.round(averageSuccessRate * 10) / 10,
      },
    };
  }

  /**
   * Log daily summary (can be called by a cron job)
   */
  logDailySummary(): void {
    const stats = this.getOverallStats();
    
    console.log(
      `📈 Daily Notification Summary: ` +
      `${stats.last24Hours.batches} batches, ` +
      `${stats.last24Hours.totalSent} sent, ` +
      `${stats.last24Hours.totalSkipped} skipped, ` +
      `${stats.last24Hours.totalFailed} failed ` +
      `(${stats.last24Hours.averageSuccessRate}% avg success rate)`
    );
  }

  /**
   * Generate a unique batch ID
   */
  generateBatchId(prefix = 'batch'): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    return `${prefix}-${timestamp}-${random}`;
  }
}
