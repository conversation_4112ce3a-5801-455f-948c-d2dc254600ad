import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { Helper } from 'src/helper/helper.interface';
import { throwNotFoundErr } from 'src/internal/exception/api.exception.ext';
import { TaskProviderForAdmin } from '../../admin/providers/admin.provider';
import { TASK_NOT_FOUND } from '../../common/const/task.const';
import { CompletionStatus } from '../../common/entities/user-task.entity';
import {
  GetUserTasksSuccessResponseDtoForUser,
  GetUserTaskSuccessResponseDtoForUser,
} from '../dtos/user-get-task.dto';
import { TaskProviderForUser } from '../providers/user.provider';

@Injectable()
export class TaskServiceForUser {
  constructor(
    private readonly taskProviderForUser: TaskProviderForUser,
    @Inject(forwardRef(() => TaskProviderForAdmin))
    private readonly taskProviderForAdmin: TaskProviderForAdmin,
    private readonly helper: Helper,
  ) {}

  // async getUserTasks1(
  //   userId: string,
  //   query: GetUserTasksQueryDtoForUser,
  // ): Promise<GetUserTasksSuccessResponseDtoForUser> {
  //   const userTasks = await this.taskProviderForUser.getUserTasks(userId, {
  //     type: query.type,
  //   });

  //   const enrichedTasks = await Promise.all(
  //     userTasks.map(async (userTask) => {
  //       // Skip completed tasks
  //       if (userTask.completionStatus === CompletionStatus.COMPLETED) {
  //         return null;
  //       }

  //       const task = await this.taskProviderForAdmin.getTaskById(
  //         userTask.taskId,
  //       );
  //       if (!task) return null;

  //       return {
  //         id: userTask.id,
  //         taskId: task.id,
  //         title: task.title,
  //         description: task.description,
  //         type: task.type,
  //         progress: userTask.progress,
  //         targetValue: task.targetValue,
  //         points: task.points,
  //         completionStatus: userTask.completionStatus,
  //         pointsAwarded: userTask.pointsAwarded,
  //         deadline: task.deadline,
  //       };
  //     }),
  //   );

  //   const filteredTasks = enrichedTasks.filter((task) => task !== null);

  //   const responseDtos = filteredTasks.map((task) =>
  //     deepCasting(GetUserTaskResponseDtoForUser, task),
  //   );

  //   return this.helper.serviceResponse.successResponse(responseDtos);
  // }

  async getUserTaskById(
    userId: string,
    taskId: string,
  ): Promise<GetUserTaskSuccessResponseDtoForUser> {
    const task = await this.taskProviderForAdmin.getTaskById(taskId);
    throwNotFoundErr(!task, 'Task not found', TASK_NOT_FOUND);

    let userTask = await this.taskProviderForUser.getUserTaskByUserAndTaskId(
      userId,
      taskId,
    );

    // If user doesn't have this task yet, create it automatically
    if (!userTask) {
      userTask = await this.taskProviderForUser.createUserTask(userId, taskId);
    }

    const enrichedTask = {
      id: userTask.id,
      taskId: task.id,
      title: task.title,
      description: task.description,
      type: task.type,
      progress: userTask.progress,
      targetValue: task.targetValue,
      points: task.points,
      completionStatus: userTask.completionStatus,
      pointsAwarded: userTask.pointsAwarded,
      deadline: task.deadline,
    };

    // const responseDto = deepCasting(
    //   GetUserTaskResponseDtoForUser,
    //   enrichedTask,
    // );
    return this.helper.serviceResponse.successResponse(enrichedTask);
  }

  async getUserTasks(
    userId: string,
    filters?: any,
  ): Promise<GetUserTasksSuccessResponseDtoForUser> {
    // Fetch all relevant tasks from admin provider
    const tasks = await this.taskProviderForAdmin.getTasks({
      ...filters,
      isActive: filters?.isActive !== undefined ? filters.isActive : true,
    });

    // Current date for deadline comparison
    const now = new Date();

    // Filter out tasks with expired deadlines
    const activeTasksWithValidDeadlines = tasks.filter((task) => {
      return !task.deadline || new Date(task.deadline) > now;
    });

    // For each task, check if there's an existing user-task
    const userTasksPromises = activeTasksWithValidDeadlines.map(
      async (task) => {
        try {
          // Only query existing user tasks, don't create new ones
          const userTask =
            await this.taskProviderForUser.getUserTaskByUserAndTaskId(
              userId,
              task.id,
            );

          return {
            id: task.id,
            title: task.title,
            // description: task.description,
            type: task.type,
            // targetValue: task.targetValue,
            requirementType: task.requirementType,
            points: task.points,
            deadline: task.deadline,
            createdAt: task.createdAt, // Add this to enable sorting by creation date
            updatedAt: task.updatedAt,
            progress: userTask?.progress || 0.0,
            completionStatus: userTask?.completionStatus || 'not_started',
            pointsAwarded: userTask?.pointsAwarded || false,
          };
        } catch (error) {
          console.error(`Error processing task ${task.id}:`, error);
          return null;
        }
      },
    );

    let userTasks = await Promise.all(userTasksPromises);

    // Filter out nulls (errors)
    userTasks = userTasks.filter((task) => task !== null);

    // Apply completion status filter if provided
    if (filters?.status) {
      if (filters.status === 'completed') {
        userTasks = userTasks.filter(
          (task) => task.completionStatus === CompletionStatus.COMPLETED,
        );
      } else if (filters.status === 'active') {
        userTasks = userTasks.filter(
          (task) =>
            task.completionStatus === CompletionStatus.IN_PROGRESS ||
            task.completionStatus === CompletionStatus.NOT_STARTED,
        );
      }
    }

    // Sort by creation date (newest first)
    userTasks.sort((a, b) => {
      const dateA = a.createdAt ? new Date(a.createdAt).getTime() : 0;
      const dateB = b.createdAt ? new Date(b.createdAt).getTime() : 0;
      return dateB - dateA; // Descending order (newest first)
    });

    return this.helper.serviceResponse.successResponse(userTasks);
  }
}
