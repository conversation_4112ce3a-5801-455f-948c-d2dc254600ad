import { <PERSON><PERSON><PERSON>, OnModuleInit } from '@nestjs/common';
import { ReminderController } from './rest/reminder.controller';
import { ReminderService } from './services/reminder.service';
import { ReminderRepository } from './repositories/reminder.repository';
import { ReminderSchedulerService } from './services/reminder-scheduler.service';
import { NotificationModule } from '../notification/notification.module';
import { TimezoneService } from '../../utils/timezone/timezone.service';

@Module({
  imports: [NotificationModule],
  controllers: [ReminderController],
  providers: [
    ReminderService,
    ReminderRepository,
    ReminderSchedulerService,
    TimezoneService,
  ],
  exports: [ReminderService, ReminderSchedulerService],
})
export class ReminderModule implements OnModuleInit {
  constructor(
    private readonly reminderService: ReminderService,
    private readonly reminderSchedulerService: ReminderSchedulerService,
  ) {}

  async onModuleInit() {
    // Set the scheduler service reference to avoid circular dependency
    (this.reminderService as any).schedulerService = this.reminderSchedulerService;
  }
}
