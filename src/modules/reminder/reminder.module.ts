import { <PERSON><PERSON><PERSON>, OnModuleInit } from '@nestjs/common';
import { <PERSON>minderController } from './rest/reminder.controller';
import { ReminderService } from './services/reminder.service';
import { ReminderRepository } from './repositories/reminder.repository';
import { ReminderSchedulerService } from './services/reminder-scheduler.service';
import { NotificationHelperService } from '../notification/services/helper.notification.service';
import { NotificationStatsService } from '../notification/services/notification-stats.service';
import { TimezoneService } from '../../utils/timezone/timezone.service';

@Module({
  controllers: [ReminderController],
  providers: [
    ReminderService,
    ReminderRepository,
    ReminderSchedulerService,
    NotificationHelperService,
    NotificationStatsService,
    TimezoneService,
  ],
  exports: [ReminderService, ReminderSchedulerService],
})
export class ReminderModule implements OnModuleInit {
  constructor(
    private readonly reminderService: ReminderService,
    private readonly reminderSchedulerService: ReminderSchedulerService,
  ) {}

  async onModuleInit() {
    // Set the scheduler service reference to avoid circular dependency
    (this.reminderService as any).schedulerService = this.reminderSchedulerService;
  }
}
