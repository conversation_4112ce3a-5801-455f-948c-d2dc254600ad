import {
  Body,
  Controller,
  Delete,
  Get,
  HttpStatus,
  Param,
  Post,
  Put,
  Req,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { RolesGuard } from 'src/authentication/guards/auth.guard';
import { ReminderService } from '../services/reminder.service';
import { ReminderSchedulerService } from '../services/reminder-scheduler.service';
import { NotificationHelperService } from '../../notification/services/helper.notification.service';
import {
  CreateReminderDto,
  GetRemindersResponseDto,
  ReminderResponseDto,
  UpdateReminderDto,
} from './dto/reminder.dto';

@ApiTags('Reminders')
@Controller('reminders')
export class ReminderController {
  constructor(
    private readonly reminderService: ReminderService,
    private readonly reminderSchedulerService: ReminderSchedulerService,
    private readonly notificationHelperService: NotificationHelperService,
  ) {}

  @UseGuards(new RolesGuard(['admin']))
  @ApiBearerAuth()
  @Post()
  @ApiOperation({ summary: 'Create a new daily reminder (Admin only)' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Daily reminder created successfully',
    type: ReminderResponseDto,
  })
  async createReminder(
    @Req() request: any,
    @Body() createReminderDto: CreateReminderDto,
  ) {
    const admin = await request.user;
    const reminder = await this.reminderService.createReminder(
      admin.id,
      createReminderDto,
    );
    return reminder;
  }

  @UseGuards(new RolesGuard(['admin']))
  @ApiBearerAuth()
  @Get()
  @ApiOperation({ summary: 'Get all daily reminders (Admin only)' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Daily reminders retrieved successfully',
    type: GetRemindersResponseDto,
  })
  async getReminders() {
    const reminders = await this.reminderService.getAllReminders();
    return { data: reminders };
  }

  @UseGuards(new RolesGuard(['admin']))
  @ApiBearerAuth()
  @Get(':id')
  @ApiOperation({ summary: 'Get a daily reminder by ID (Admin only)' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Daily reminder retrieved successfully',
    type: ReminderResponseDto,
  })
  async getReminderById(@Param('id') id: string) {
    return await this.reminderService.getReminderById(id);
  }

  @UseGuards(new RolesGuard(['admin']))
  @ApiBearerAuth()
  @Put(':id')
  @ApiOperation({ summary: 'Update a daily reminder (Admin only)' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Daily reminder updated successfully',
    type: ReminderResponseDto,
  })
  async updateReminder(
    @Param('id') id: string,
    @Body() updateReminderDto: UpdateReminderDto,
  ) {
    return await this.reminderService.updateReminder(id, updateReminderDto);
  }

  @UseGuards(new RolesGuard(['admin']))
  @ApiBearerAuth()
  @Delete(':id')
  @ApiOperation({ summary: 'Delete a daily reminder (Admin only)' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Daily reminder deleted successfully',
  })
  async deleteReminder(@Param('id') id: string) {
    const deleted = await this.reminderService.deleteReminder(id);
    return { success: deleted };
  }

  @UseGuards(new RolesGuard(['admin']))
  @ApiBearerAuth()
  @Get('scheduler/status')
  @ApiOperation({ summary: 'Get smart scheduler status (Admin only)' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Scheduler status retrieved successfully',
  })
  async getSchedulerStatus() {
    return this.reminderSchedulerService.getSchedulerStatus();
  }

  @UseGuards(new RolesGuard(['admin']))
  @ApiBearerAuth()
  @Post('scheduler/refresh')
  @ApiOperation({ summary: 'Manually refresh smart scheduler (Admin only)' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Scheduler refreshed successfully',
  })
  async refreshScheduler() {
    return await this.reminderSchedulerService.manualRefresh();
  }

  @UseGuards(new RolesGuard(['admin']))
  @ApiBearerAuth()
  @Post('test/send-now')
  @ApiOperation({ summary: 'Test reminder notifications - send now (Admin only)' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Test notifications sent',
  })
  async testSendReminders() {
    try {
      // Get all active reminders
      const allReminders = await this.reminderService.getAllReminders();
      const activeReminders = allReminders.filter(r => r.isActive);

      if (activeReminders.length === 0) {
        return {
          success: false,
          message: 'No active reminders found',
          activeReminders: 0,
          totalReminders: allReminders.length
        };
      }

      // Manually trigger reminder notifications
      await this.reminderService.sendReminderNotifications();

      return {
        success: true,
        message: 'Test notifications triggered',
        activeReminders: activeReminders.length,
        totalReminders: allReminders.length,
        reminders: activeReminders.map(r => ({
          id: r.id,
          title: r.title,
          time: r.time,
          isRepeating: r.isRepeating,
          repeatDays: r.repeatDays
        }))
      };
    } catch (error) {
      return {
        success: false,
        message: error.message,
        error: error.stack
      };
    }
  }

  @UseGuards(new RolesGuard(['admin']))
  @ApiBearerAuth()
  @Get('debug/info')
  @ApiOperation({ summary: 'Get debug information about reminder system (Admin only)' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Debug information retrieved',
  })
  async getDebugInfo() {
    try {
      const allReminders = await this.reminderService.getAllReminders();
      const activeReminders = allReminders.filter(r => r.isActive);
      const schedulerStatus = this.reminderSchedulerService.getSchedulerStatus();

      // Get current time info
      const now = new Date();
      const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
      const currentDay = now.toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase();

      return {
        currentTime,
        currentDay,
        totalReminders: allReminders.length,
        activeReminders: activeReminders.length,
        schedulerStatus,
        reminders: allReminders.map(r => ({
          id: r.id,
          title: r.title,
          time: r.time,
          isActive: r.isActive,
          isRepeating: r.isRepeating,
          repeatDays: r.repeatDays,
          createdAt: r.createdAt
        })),
        environment: {
          processENV: process.env.ENV || 'NOT_SET',
          nodeEnv: process.env.NODE_ENV || 'NOT_SET'
        }
      };
    } catch (error) {
      return {
        success: false,
        message: error.message,
        error: error.stack
      };
    }
  }

  @UseGuards(new RolesGuard(['admin']))
  @ApiBearerAuth()
  @Get('debug/users')
  @ApiOperation({ summary: 'Get debug information about users with FCM tokens (Admin only)' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'User debug information retrieved',
  })
  async getUserDebugInfo() {
    try {
      const allUsers = await this.notificationHelperService.getAllActiveUsers();

      return {
        totalActiveUsers: allUsers.length,
        usersWithFcmTokens: allUsers.filter(u => u.fcmToken).length,
        users: allUsers.slice(0, 5).map(u => ({
          id: u.id,
          name: u.name,
          hasFcmToken: !!u.fcmToken,
          fcmTokenLength: u.fcmToken ? u.fcmToken.length : 0,
          timezone: u.timezone || 'Asia/Dhaka'
        })),
        environment: {
          processENV: process.env.ENV || 'NOT_SET',
          sharedConfigProcessENV: require('../../../config/shared').sharedConfig.processENV
        }
      };
    } catch (error) {
      return {
        success: false,
        message: error.message,
        error: error.stack
      };
    }
  }
}
