import {
  Body,
  Controller,
  Delete,
  Get,
  HttpStatus,
  Param,
  Post,
  Put,
  Req,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { RolesGuard } from 'src/authentication/guards/auth.guard';
import { ReminderService } from '../services/reminder.service';
import { ReminderSchedulerService } from '../services/reminder-scheduler.service';
import {
  CreateReminderDto,
  GetRemindersResponseDto,
  ReminderResponseDto,
  UpdateReminderDto,
} from './dto/reminder.dto';

@ApiTags('Reminders')
@Controller('reminders')
export class ReminderController {
  constructor(
    private readonly reminderService: ReminderService,
    private readonly reminderSchedulerService: ReminderSchedulerService,
  ) {}

  @UseGuards(new RolesGuard(['admin']))
  @ApiBearerAuth()
  @Post()
  @ApiOperation({ summary: 'Create a new daily reminder (Admin only)' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Daily reminder created successfully',
    type: ReminderResponseDto,
  })
  async createReminder(
    @Req() request: any,
    @Body() createReminderDto: CreateReminderDto,
  ) {
    const admin = await request.user;
    const reminder = await this.reminderService.createReminder(
      admin.id,
      createReminderDto,
    );
    return reminder;
  }

  @UseGuards(new RolesGuard(['admin']))
  @ApiBearerAuth()
  @Get()
  @ApiOperation({ summary: 'Get all daily reminders (Admin only)' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Daily reminders retrieved successfully',
    type: GetRemindersResponseDto,
  })
  async getReminders() {
    const reminders = await this.reminderService.getAllReminders();
    return { data: reminders };
  }

  @UseGuards(new RolesGuard(['admin']))
  @ApiBearerAuth()
  @Get(':id')
  @ApiOperation({ summary: 'Get a daily reminder by ID (Admin only)' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Daily reminder retrieved successfully',
    type: ReminderResponseDto,
  })
  async getReminderById(@Param('id') id: string) {
    return await this.reminderService.getReminderById(id);
  }

  @UseGuards(new RolesGuard(['admin']))
  @ApiBearerAuth()
  @Put(':id')
  @ApiOperation({ summary: 'Update a daily reminder (Admin only)' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Daily reminder updated successfully',
    type: ReminderResponseDto,
  })
  async updateReminder(
    @Param('id') id: string,
    @Body() updateReminderDto: UpdateReminderDto,
  ) {
    return await this.reminderService.updateReminder(id, updateReminderDto);
  }

  @UseGuards(new RolesGuard(['admin']))
  @ApiBearerAuth()
  @Delete(':id')
  @ApiOperation({ summary: 'Delete a daily reminder (Admin only)' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Daily reminder deleted successfully',
  })
  async deleteReminder(@Param('id') id: string) {
    const deleted = await this.reminderService.deleteReminder(id);
    return { success: deleted };
  }

  @UseGuards(new RolesGuard(['admin']))
  @ApiBearerAuth()
  @Get('scheduler/status')
  @ApiOperation({ summary: 'Get smart scheduler status (Admin only)' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Scheduler status retrieved successfully',
  })
  async getSchedulerStatus() {
    return this.reminderSchedulerService.getSchedulerStatus();
  }

  @UseGuards(new RolesGuard(['admin']))
  @ApiBearerAuth()
  @Post('scheduler/refresh')
  @ApiOperation({ summary: 'Manually refresh smart scheduler (Admin only)' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Scheduler refreshed successfully',
  })
  async refreshScheduler() {
    return await this.reminderSchedulerService.manualRefresh();
  }
}
