import { Injectable } from '@nestjs/common';
import { ReminderRepository } from '../repositories/reminder.repository';
import { CreateReminderDto, UpdateReminderDto } from '../rest/dto/reminder.dto';
import { Reminder } from 'src/entity/reminder';
import { NotificationHelperService } from 'src/modules/notification/services/helper.notification.service';
import { NotificationStatsService } from 'src/modules/notification/services/notification-stats.service';
import {
  NotificationCategory,
  NotificationModule,
  NotificationType,
} from 'models';
import { QueueInstance } from 'src/queue-system';
import {
  NotificationQueueName,
  QueuePayloadType,
} from 'src/queue-system/predefined-data';

@Injectable()
export class ReminderService {
  private schedulerService: any; // Will be injected by module to avoid circular dependency

  constructor(
    private readonly reminderRepository: ReminderRepository,
    private readonly notificationHelperService: NotificationHelperService,
    private readonly notificationStatsService: NotificationStatsService,
  ) {}

  async createReminder(
    adminId: string,
    createReminderDto: CreateReminderDto,
  ): Promise<Reminder> {
    const reminder = await this.reminderRepository.createReminder(adminId, createReminderDto);

    // Refresh scheduler when reminder is created
    if (this.schedulerService && reminder) {
      await this.schedulerService.onReminderChange();
    }

    return reminder;
  }

  async getAllReminders(): Promise<Reminder[]> {
    return this.reminderRepository.findAllReminders();
  }

  async getReminderById(id: string): Promise<Reminder> {
    return this.reminderRepository.findReminderById(id);
  }

  async updateReminder(
    id: string,
    updateReminderDto: UpdateReminderDto,
  ): Promise<Reminder> {
    const reminder = await this.reminderRepository.updateReminder(id, updateReminderDto);

    // Refresh scheduler when reminder is updated
    if (this.schedulerService && reminder) {
      await this.schedulerService.onReminderChange();
    }

    return reminder;
  }

  async deleteReminder(id: string): Promise<boolean> {
    const deleted = await this.reminderRepository.deleteReminder(id);

    // Refresh scheduler when reminder is deleted
    if (this.schedulerService && deleted) {
      await this.schedulerService.onReminderChange();
    }

    return deleted;
  }

  async sendReminderNotifications(): Promise<void> {
    try {
      // Get current time in HH:MM format
      const now = new Date();
      const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now
        .getMinutes()
        .toString()
        .padStart(2, '0')}`;

      // Find all active reminders for the current time
      const activeReminders =
        await this.reminderRepository.findActiveRemindersForTime(currentTime);

      if (activeReminders.length === 0) {
        return;
      }

      // Get all active users with FCM tokens
      const allUsers = await this.notificationHelperService.getAllActiveUsers();

      if (allUsers.length === 0) {
        return;
      }

      // Calculate total notifications to send
      const totalNotifications = activeReminders.length * allUsers.length;

      // Start batch tracking
      const batchId = this.notificationStatsService.generateBatchId('legacy-reminder');
      this.notificationStatsService.startBatch(
        batchId,
        totalNotifications,
        activeReminders.map(r => r.title).join(', '),
        'mixed'
      );

      // Send notifications for each reminder to ALL users
      for (const reminder of activeReminders) {
        // Send notification to each user using the same queue system as spot requests
        for (const user of allUsers) {
          if (user.fcmToken) {
            // Use the same queue system as spot requests
            const payload = {
              targetUsers: [user.id],
              createdBy: {
                name: 'Fitsomnia',
                userId: 'system',
                avatar: null,
              },
              fcmToken: user.fcmToken,
              payloadType: QueuePayloadType.DAILY_REMINDER_GENERATOR,
              module: NotificationModule.REMINDER,
              type: NotificationType.DAILY_REMINDER,
              title: reminder.title,
              content: reminder.message,
              category: NotificationCategory.DAILY_REMINDER,
              documentId: reminder.id,
              saveDatabase: true,
              task: 'generator',
            };

            // Send to queue (same pattern as spot requests)
            try {
              const queueInstance = await QueueInstance;
              if (!queueInstance) {
                throw new Error('Queue instance is not available');
              }
              await queueInstance.sendPayload(
                NotificationQueueName.DAILY_REMINDER_GENERATOR_QUEUE,
                Buffer.from(JSON.stringify(payload)),
              );
              // Track successful send
              this.notificationStatsService.incrementSent(batchId);
            } catch (error) {
              // Track failed send
              this.notificationStatsService.incrementFailed(batchId);
              console.error(`Failed to queue reminder for user ${user.name}:`, error.message);
            }
          } else {
            // Track skipped (no FCM token)
            this.notificationStatsService.incrementSkipped(batchId);
          }
        }
      }

      // Complete batch and log aggregate stats
      this.notificationStatsService.completeBatch(batchId);
    } catch (error) {
      console.error('Error sending reminder notifications:', error.message);
    }
  }

  async sendReminderNotificationsForTimezone(time: string, timezone: string): Promise<void> {
    try {
      // Get active reminders for this time
      const activeReminders = await this.reminderRepository.findActiveRemindersForTime(time);

      if (activeReminders.length === 0) {
        return;
      }

      // Get users in this timezone
      const users = await this.notificationHelperService.getAllActiveUsers();
      const timezoneUsers = users.filter(user => (user.timezone || 'Asia/Dhaka') === timezone);

      if (timezoneUsers.length === 0) {
        return;
      }

      // Calculate total notifications to send
      const totalNotifications = activeReminders.length * timezoneUsers.length;

      // Start batch tracking
      const batchId = this.notificationStatsService.generateBatchId('reminder');
      this.notificationStatsService.startBatch(
        batchId,
        totalNotifications,
        activeReminders.map(r => r.title).join(', '),
        timezone
      );

      // Send notifications to users in batches
      const batchSize = 500;
      for (let i = 0; i < timezoneUsers.length; i += batchSize) {
        const userBatch = timezoneUsers.slice(i, i + batchSize);
        await this.sendNotificationsBatch(activeReminders, userBatch, timezone, batchId);
      }

      // Complete batch and log aggregate stats
      this.notificationStatsService.completeBatch(batchId);
    } catch (error) {
      console.error(`Error sending timezone-aware reminders for ${timezone}:`, error.message);
    }
  }

  private async sendNotificationsBatch(reminders: Reminder[], users: any[], timezone: string, batchId: string): Promise<void> {
    try {
      for (const reminder of reminders) {
        for (const user of users) {
          if (user.fcmToken) {
            try {
              // Use the notification helper service to send notifications
              await this.notificationHelperService.sendNotifications({
                module: NotificationModule.REMINDER,
                title: reminder.title,
                content: reminder.message,
                date: new Date(),
                category: NotificationCategory.DAILY_REMINDER,
                documentId: reminder.id,
                body: reminder.message,
                data: {
                  reminderId: reminder.id,
                  timezone: timezone,
                  localTime: reminder.time,
                },
                type: NotificationType.DAILY_REMINDER,
                createdBy: {
                  id: 'system',
                  name: 'FitSomnia',
                  image: {
                    profile: '',
                  },
                },
                recipient: {
                  id: user.id,
                  name: user.name,
                  image: user.image || { profile: '' },
                  fcmToken: user.fcmToken,
                },
              });
              // Track successful send
              this.notificationStatsService.incrementSent(batchId);
            } catch (queueError) {
              // Track failed send
              this.notificationStatsService.incrementFailed(batchId);
              console.error(`Failed to send notification to user ${user.name}:`, queueError.message);
            }
          } else {
            // Track skipped (no FCM token)
            this.notificationStatsService.incrementSkipped(batchId);
          }
        }
      }
    } catch (error) {
      console.error('Error in sendNotificationsBatch:', error.message);
    }
  }
}
