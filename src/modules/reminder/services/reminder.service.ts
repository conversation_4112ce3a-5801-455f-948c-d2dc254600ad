import { Injectable } from '@nestjs/common';
import { ReminderRepository } from '../repositories/reminder.repository';
import { CreateReminderDto, UpdateReminderDto } from '../rest/dto/reminder.dto';
import { Reminder } from 'src/entity/reminder';
import { NotificationHelperService } from 'src/modules/notification/services/helper.notification.service';
import {
  NotificationCategory,
  NotificationModule,
  NotificationType,
} from 'models';
import { QueueInstance } from 'src/queue-system';
import {
  NotificationQueueName,
  QueuePayloadType,
} from 'src/queue-system/predefined-data';

@Injectable()
export class ReminderService {
  private schedulerService: any; // Will be injected by module to avoid circular dependency

  constructor(
    private readonly reminderRepository: ReminderRepository,
    private readonly notificationHelperService: NotificationHelperService,
  ) {}

  async createReminder(
    adminId: string,
    createReminderDto: CreateReminderDto,
  ): Promise<Reminder> {
    const reminder = await this.reminderRepository.createReminder(adminId, createReminderDto);

    // Refresh scheduler when reminder is created
    if (this.schedulerService && reminder) {
      await this.schedulerService.onReminderChange();
    }

    return reminder;
  }

  async getAllReminders(): Promise<Reminder[]> {
    return this.reminderRepository.findAllReminders();
  }

  async getReminderById(id: string): Promise<Reminder> {
    return this.reminderRepository.findReminderById(id);
  }

  async updateReminder(
    id: string,
    updateReminderDto: UpdateReminderDto,
  ): Promise<Reminder> {
    const reminder = await this.reminderRepository.updateReminder(id, updateReminderDto);

    // Refresh scheduler when reminder is updated
    if (this.schedulerService && reminder) {
      await this.schedulerService.onReminderChange();
    }

    return reminder;
  }

  async deleteReminder(id: string): Promise<boolean> {
    const deleted = await this.reminderRepository.deleteReminder(id);

    // Refresh scheduler when reminder is deleted
    if (this.schedulerService && deleted) {
      await this.schedulerService.onReminderChange();
    }

    return deleted;
  }

  async sendReminderNotifications(): Promise<void> {
    try {
      console.log('🔔 Starting reminder notification process...');

      // Get current time in HH:MM format
      const now = new Date();
      const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now
        .getMinutes()
        .toString()
        .padStart(2, '0')}`;

      console.log(`⏰ Current time: ${currentTime}`);

      // Find all active reminders for the current time
      const activeReminders =
        await this.reminderRepository.findActiveRemindersForTime(currentTime);

      console.log(
        `📋 Found ${activeReminders.length} active reminders for time ${currentTime}`,
      );

      if (activeReminders.length === 0) {
        console.log('❌ No active reminders found for current time');
        return;
      }

      // Send notifications for each reminder to ALL users
      for (const reminder of activeReminders) {
        console.log(
          `📝 Processing reminder: "${reminder.title}" (ID: ${reminder.id})`,
        );

        // Get all active users with FCM tokens
        const allUsers =
          await this.notificationHelperService.getAllActiveUsers();

        console.log(
          `👥 Found ${allUsers.length} users with FCM tokens for reminder: ${reminder.title}`,
        );

        if (allUsers.length === 0) {
          console.log('❌ No users with FCM tokens found');
          continue;
        }

        // Send notification to each user using the same queue system as spot requests
        for (const user of allUsers) {
          if (user.fcmToken) {
            console.log(
              `📤 Sending reminder to user: ${user.name} (${user.id})`,
            );

            // Use the same queue system as spot requests
            const payload = {
              targetUsers: [user.id],
              createdBy: {
                name: 'Fitsomnia',
                userId: 'system',
                avatar: null,
              },
              fcmToken: user.fcmToken,
              payloadType: QueuePayloadType.DAILY_REMINDER_GENERATOR,
              module: NotificationModule.REMINDER,
              type: NotificationType.DAILY_REMINDER,
              title: reminder.title,
              content: reminder.message,
              category: NotificationCategory.DAILY_REMINDER,
              documentId: reminder.id,
              saveDatabase: true,
              task: 'generator',
            };

            // Send to queue (same pattern as spot requests)
            try {
              const queueInstance = await QueueInstance;
              if (!queueInstance) {
                throw new Error('Queue instance is not available');
              }
              await queueInstance.sendPayload(
                NotificationQueueName.DAILY_REMINDER_GENERATOR_QUEUE,
                Buffer.from(JSON.stringify(payload)),
              );
              console.log(
                `📤 Sending reminder to user: ${user.name} (${user.id})`,
              );
            } catch (error) {
              console.error(
                `❌ Failed to queue reminder for user ${user.name}:`,
                error.message,
              );
            }
          } else {
            console.log(`⚠️ User ${user.name} has no FCM token`);
          }
        }
      }

      console.log(
        `🎉 Completed sending ${activeReminders.length} reminder notifications to all users at ${currentTime}`,
      );
    } catch (error) {
      console.error('💥 Error sending reminder notifications:', error.message);
    }
  }

  async sendReminderNotificationsForTimezone(time: string, timezone: string): Promise<void> {
    try {
      console.log(`🔔 Sending timezone-aware reminders for ${timezone} at ${time}`);

      // Get active reminders for this time
      const activeReminders = await this.reminderRepository.findActiveRemindersForTime(time);

      if (activeReminders.length === 0) {
        console.log(`ℹ️ No active reminders found for ${time} in ${timezone}`);
        return;
      }

      // Get users in this timezone
      const users = await this.notificationHelperService.getAllActiveUsers();
      const timezoneUsers = users.filter(user => (user.timezone || 'Asia/Dhaka') === timezone);

      if (timezoneUsers.length === 0) {
        console.log(`ℹ️ No users found in timezone ${timezone}`);
        return;
      }

      console.log(`📱 Sending ${activeReminders.length} reminders to ${timezoneUsers.length} users in ${timezone}`);

      // Send notifications to users in batches
      const batchSize = 500;
      for (let i = 0; i < timezoneUsers.length; i += batchSize) {
        const userBatch = timezoneUsers.slice(i, i + batchSize);
        await this.sendNotificationsBatch(activeReminders, userBatch, timezone);
      }

      console.log(`✅ Completed timezone-aware reminders for ${timezone} at ${time}`);
    } catch (error) {
      console.error(`💥 Error sending timezone-aware reminders for ${timezone}:`, error.message);
    }
  }

  private async sendNotificationsBatch(reminders: Reminder[], users: any[], timezone: string): Promise<void> {
    try {
      for (const reminder of reminders) {
        for (const user of users) {
          if (user.fcmToken) {
            try {
              // Use the notification helper service to send notifications
              await this.notificationHelperService.sendNotifications({
                module: NotificationModule.REMINDER,
                title: reminder.title,
                content: reminder.message,
                date: new Date(),
                category: NotificationCategory.DAILY_REMINDER,
                documentId: reminder.id,
                body: reminder.message,
                data: {
                  reminderId: reminder.id,
                  timezone: timezone,
                  localTime: reminder.time,
                },
                type: NotificationType.DAILY_REMINDER,
                createdBy: {
                  id: 'system',
                  name: 'FitSomnia',
                  image: {
                    profile: '',
                  },
                },
                recipient: {
                  id: user.id,
                  name: user.name,
                  image: user.image || { profile: '' },
                  fcmToken: user.fcmToken,
                },
              });
            } catch (queueError) {
              console.error(`❌ Failed to send notification to user ${user.name}:`, queueError.message);
            }
          }
        }
      }
    } catch (error) {
      console.error('❌ Error in sendNotificationsBatch:', error.message);
    }
  }
}
