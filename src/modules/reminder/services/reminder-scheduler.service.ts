import { Injectable, OnModuleInit } from '@nestjs/common';
import { Cron, CronExpression, SchedulerRegistry } from '@nestjs/schedule';
import { <PERSON>ronJob } from 'cron';
import { ReminderService } from './reminder.service';
import { ReminderRepository } from '../repositories/reminder.repository';
import { TimezoneService } from '../../../utils/timezone/timezone.service';
import { NotificationHelperService } from '../../notification/services/helper.notification.service';

interface TimezoneReminderGroup {
  timezone: string;
  time: string;
  reminderIds: Set<string>;
  userCount: number;
}

@Injectable()
export class ReminderSchedulerService implements OnModuleInit {
  private activeJobs = new Map<string, CronJob>();
  private timezoneReminderCache = new Map<string, TimezoneReminderGroup[]>(); // time -> timezone groups
  private lastCacheUpdate = 0;
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes

  constructor(
    private readonly reminderService: ReminderService,
    private readonly reminderRepository: ReminderRepository,
    private readonly schedulerRegistry: SchedulerRegistry,
    private readonly timezoneService: TimezoneService,
    private readonly notificationHelperService: NotificationHelperService,
  ) {}

  async onModuleInit() {
    console.log('🚀 Initializing Timezone-Aware Smart Reminder Scheduler...');
    await this.initializeSmartScheduling();
  }

  /**
   * Fallback cron job that runs every 10 minutes to ensure no reminders are missed
   * This provides a safety net in case dynamic scheduling fails
   */
  @Cron('*/10 * * * *') // Every 10 minutes
  async fallbackReminderCheck() {
    console.log('🔄 Running fallback reminder check...');
    await this.refreshScheduleCache();
    await this.ensureAllRemindersScheduled();
  }

  /**
   * Initialize smart scheduling by loading all active reminders and creating dynamic cron jobs
   */
  async initializeSmartScheduling() {
    try {
      await this.refreshScheduleCache();
      await this.createDynamicCronJobs();
      console.log('✅ Timezone-aware smart scheduling initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize smart scheduling:', error.message);
      // Fall back to traditional scheduling if smart scheduling fails
      this.createFallbackJob();
    }
  }

  /**
   * Refresh the timezone-aware reminder cache by grouping reminders by timezone and time
   */
  async refreshScheduleCache() {
    try {
      const now = Date.now();
      if (now - this.lastCacheUpdate < this.CACHE_TTL) {
        return; // Cache is still fresh
      }

      console.log('🔄 Refreshing timezone-aware reminder cache...');
      const activeReminders = await this.reminderRepository.findAllActiveReminders();

      this.timezoneReminderCache.clear();

      // Group reminders by time and timezone
      for (const reminder of activeReminders) {
        const time = reminder.time;

        // Get users for this reminder grouped by timezone
        const usersByTimezone = await this.getUsersByTimezone(reminder.id);

        for (const [timezone, users] of usersByTimezone.entries()) {
          if (users.length === 0) continue;

          const cacheKey = time;
          if (!this.timezoneReminderCache.has(cacheKey)) {
            this.timezoneReminderCache.set(cacheKey, []);
          }

          const timezoneGroups = this.timezoneReminderCache.get(cacheKey);
          let existingGroup = timezoneGroups.find(g => g.timezone === timezone);

          if (!existingGroup) {
            existingGroup = {
              timezone,
              time,
              reminderIds: new Set(),
              userCount: 0,
            };
            timezoneGroups.push(existingGroup);
          }

          existingGroup.reminderIds.add(reminder.id);
          existingGroup.userCount += users.length;
        }
      }

      this.lastCacheUpdate = now;
      console.log(`✅ Cache refreshed with ${this.timezoneReminderCache.size} time slots across multiple timezones`);
    } catch (error) {
      console.error('❌ Failed to refresh schedule cache:', error.message);
    }
  }

  /**
   * Get users grouped by timezone for a specific reminder
   */
  async getUsersByTimezone(reminderId: string): Promise<Map<string, any[]>> {
    try {
      const users = await this.notificationHelperService.getAllActiveUsers();
      const usersByTimezone = new Map<string, any[]>();

      for (const user of users) {
        const timezone = user.timezone || 'Asia/Dhaka'; // Default timezone
        if (!usersByTimezone.has(timezone)) {
          usersByTimezone.set(timezone, []);
        }
        usersByTimezone.get(timezone).push(user);
      }

      return usersByTimezone;
    } catch (error) {
      console.error('❌ Failed to get users by timezone:', error.message);
      return new Map();
    }
  }

  /**
   * Create dynamic cron jobs for each timezone and time combination
   */
  async createDynamicCronJobs() {
    try {
      // Clear existing jobs
      this.clearAllJobs();

      for (const [time, timezoneGroups] of this.timezoneReminderCache) {
        for (const group of timezoneGroups) {
          await this.createCronJobForTimezone(time, group);
        }
      }

      console.log(`✅ Created ${this.activeJobs.size} timezone-aware cron jobs`);
    } catch (error) {
      console.error('❌ Failed to create dynamic cron jobs:', error.message);
    }
  }

  /**
   * Create a cron job for a specific timezone and time
   */
  async createCronJobForTimezone(time: string, group: TimezoneReminderGroup) {
    try {
      const [hour, minute] = time.split(':').map(Number);

      // Convert local time to UTC for the specific timezone
      const utcCron = this.timezoneService.convertCronToUTC(
        `${minute} ${hour} * * *`,
        group.timezone
      );

      const jobName = `reminder-${group.timezone}-${time}`;

      const job = new CronJob(
        utcCron,
        async () => {
          console.log(`🔔 Executing timezone-aware reminder for ${group.timezone} at ${time} (${group.userCount} users)`);
          await this.reminderService.sendReminderNotificationsForTimezone(time, group.timezone);
        },
        null,
        false,
        'UTC' // Always run in UTC since we converted the cron expression
      );

      this.activeJobs.set(jobName, job);
      this.schedulerRegistry.addCronJob(jobName, job);
      job.start();

      console.log(`✅ Created cron job: ${jobName} (${utcCron}) for ${group.userCount} users`);
    } catch (error) {
      console.error(`❌ Failed to create cron job for ${group.timezone} at ${time}:`, error.message);
    }
  }

  /**
   * Clear all active cron jobs
   */
  clearAllJobs() {
    for (const [jobName, job] of this.activeJobs) {
      try {
        job.stop();
        this.schedulerRegistry.deleteCronJob(jobName);
      } catch (error) {
        console.error(`❌ Failed to clear job ${jobName}:`, error.message);
      }
    }
    this.activeJobs.clear();
    console.log('🧹 Cleared all active cron jobs');
  }

  /**
   * Ensure all reminders are scheduled (fallback method)
   */
  async ensureAllRemindersScheduled() {
    try {
      const hasActiveReminders = await this.reminderRepository.hasActiveRemindersForTimeRange();
      if (hasActiveReminders && this.activeJobs.size === 0) {
        console.log('⚠️ No active jobs found but reminders exist. Reinitializing...');
        await this.initializeSmartScheduling();
      }
    } catch (error) {
      console.error('❌ Failed to ensure reminders are scheduled:', error.message);
    }
  }

  /**
   * Create a fallback job that runs every 5 minutes as emergency backup
   */
  createFallbackJob() {
    try {
      const fallbackJob = new CronJob(
        '*/5 * * * *', // Every 5 minutes
        async () => {
          console.log('🚨 Emergency fallback reminder check...');
          await this.reminderService.sendReminderNotifications();
        },
        null,
        false,
        'UTC'
      );

      this.activeJobs.set('emergency-fallback', fallbackJob);
      this.schedulerRegistry.addCronJob('emergency-fallback', fallbackJob);
      fallbackJob.start();

      console.log('🚨 Emergency fallback job created (runs every 5 minutes)');
    } catch (error) {
      console.error('❌ Failed to create fallback job:', error.message);
    }
  }

  /**
   * Get current scheduler status for monitoring
   */
  getSchedulerStatus() {
    const activeJobNames = Array.from(this.activeJobs.keys());
    const cacheSize = this.timezoneReminderCache.size;
    const lastUpdate = new Date(this.lastCacheUpdate).toISOString();

    return {
      activeJobs: activeJobNames.length,
      jobNames: activeJobNames,
      cacheSize,
      lastCacheUpdate: lastUpdate,
      cacheAge: Date.now() - this.lastCacheUpdate,
      isHealthy: activeJobNames.length > 0 || cacheSize === 0,
    };
  }

  /**
   * Manually refresh scheduling (for admin use)
   */
  async manualRefresh() {
    console.log('🔄 Manual scheduler refresh requested...');
    this.lastCacheUpdate = 0; // Force cache refresh
    await this.initializeSmartScheduling();
    return this.getSchedulerStatus();
  }

  /**
   * Called when reminders are created, updated, or deleted
   */
  async onReminderChange() {
    console.log('🔄 Reminder change detected, refreshing scheduler...');
    this.lastCacheUpdate = 0; // Force cache refresh
    await this.refreshScheduleCache();
    await this.createDynamicCronJobs();
  }
}
