import { Injectable } from "@nestjs/common";
import { UserActivityStreakModel } from "src/database/user/userActivityStreak.model";
import { UserActivityStreak } from "src/entity/user-activity-streak";

@Injectable()
export class UserActivityStreakRepository {

  async create(info: UserActivityStreak): Promise<UserActivityStreak> {
    const streak = new UserActivityStreakModel(info);
    const streakDocument = await streak.save();
    return streakDocument.toObject();
  }

  async getStreak(userId: string, isCurrentStreak: boolean): Promise<UserActivityStreak | null> {
    const searchQuery = { userId, isCurrentStreak };
    const streakDoc = await UserActivityStreakModel.findOne(searchQuery).exec();
    return streakDoc !== null ? streakDoc.toObject() : null;
  }

  async updateStreak(userId: string, info: UserActivityStreak): Promise<UserActivityStreak> {
    const updatedDoc = await UserActivityStreakModel.findOneAndUpdate(
      { userId },
      { $set: info },
      { new: true, runValidators: true }
    ).exec();
    return updatedDoc !== null ? updatedDoc.toObject() : null;
  }

  async deleteStreak(userId: string): Promise<UserActivityStreak> {
    const newInfo = { isDeleted: true };
    const updatedDoc = await UserActivityStreakModel.findOneAndUpdate(
      { userId },
      { $set: newInfo },
      { new: true, runValidators: true }
    ).exec();
    return updatedDoc !== null ? updatedDoc.toObject() : null;
  }
}
