import { InjectRedis } from '@nestjs-modules/ioredis';
import { Inject, Injectable, forwardRef } from '@nestjs/common';
import { userActivityStreakConfig } from 'config/user-activity-streak';
import { Redis } from 'ioredis';
import { IUserPointsSources, IUserPointsTransTypeEnum } from 'models';
import {
  UserActivityStreakHistoryResponse,
  UserWeeklyStreakResponse,
} from 'src/entity/user-activity-streak';
import { Helper } from 'src/helper/helper.interface';
import { RedisDBService } from 'src/modules/chat/realtime/redis/redis.db.service';
import { UserActivityStreakRepository } from 'src/modules/user-activity-streak/repositories/user-activity-streak.repository';
import { UserDailyActivityRepository } from 'src/modules/user-activity-streak/repositories/user-daily-activity.repository';
import { UserPointsProvider } from 'src/modules/user-points/providers/user-points.provider';
import {
  GetUserMonthlyStreakResponseDtoForUser,
  GetUserMonthlyStreakSuccessResponseDto,
  GetUserWeeklyStreakSuccessResponseDto,
} from '../dtos/get-activity-streak.dto';

@Injectable()
export class UserActivityStreakService {
  constructor(
    @InjectRedis() private readonly redis: Redis,
    private readonly dailyRepo: UserDailyActivityRepository,
    private readonly streakRepo: UserActivityStreakRepository,
    private readonly userPointsProvider: UserPointsProvider,
    private readonly helper: Helper,
    @Inject(forwardRef(() => RedisDBService))
    private readonly redisDBService: RedisDBService,
  ) {}

  private async checkTodayStreak(userId: string) {
    // check if today qualifies
    console.log(`Checking today's streak for user ${userId}`);
    const todayActivity = await this.dailyRepo.getTodayActivity(userId);
    console.log(`Today's activity for user ${userId}:`, todayActivity);
    const storedActivityDuration = todayActivity?.activityDuration || 0;

    console.log(
      `Stored activity duration for today is ${storedActivityDuration} minutes`,
    );
    if (
      storedActivityDuration >=
      userActivityStreakConfig.userActivityStreakMinutesPerDay
    ) {
      await this.dailyRepo.updateActivityMetRequirement(
        userId,
        this.formatDateToUTC6(),
        true,
      );
      console.log(
        'checkTodayStreak',
        storedActivityDuration,
        userActivityStreakConfig.userActivityStreakMinutesPerDay,
      );
      return true;
    }

    const clientId = await this.redisDBService.getClientIdByUserId(userId);
    if (!clientId) {
      console.log(`No clientId found for user ${userId}`);
      return false; // no clientId
    }

    const start = await this.redis.get('STIME_' + clientId);
    if (!start) {
      console.log(`No start time found for clientId ${clientId}`);
      return false; // no start time
    }

    console.log(`Found start time for clientId ${clientId}: ${start}`);
    const end = Date.now();
    const millis = end - Number(start);
    const minutes = Math.ceil(millis / 60000);
    const todayTotalTime = storedActivityDuration + minutes;

    console.log(
      `Updating today's activity requirement for user ${userId} with total time ${todayTotalTime} minutes`,
    );
    await this.dailyRepo.updateActivityMetRequirement(
      userId,
      this.formatDateToUTC6(),
      todayTotalTime >=
        userActivityStreakConfig.userActivityStreakMinutesPerDay,
    );
    console.log('requirement updated');
    return (
      todayTotalTime >= userActivityStreakConfig.userActivityStreakMinutesPerDay
    );
  }

  private getDateInUTC6(date: Date = new Date()): Date {
    return new Date(date.getTime() + 6 * 60 * 60 * 1000);
  }

  private formatDateToUTC6(date: Date = new Date()): string {
    return this.getDateInUTC6(date).toISOString().slice(0, 10);
  }

  /**
   * Records a session for a user, and updates their daily activity,
   * and streak. If the user has reached the threshold, it rewards them.
   * @param userId The user ID to record the session for.
   * @param startTime The date the session started.
   * @param endTime The date the session ended.
   * @param millis The number of milliseconds the session was active.
   */
  async saveSession(
    userId: string,
    startTime: Date,
    endTime: Date,
    millis: number,
  ) {
    console.log('Save session', userId, startTime, endTime, millis);
    const minutes = Math.ceil(millis / 60000);

    // upsert daily minutes with UTC+6
    const dateStr = this.formatDateToUTC6();
    const daily = await this.dailyRepo.addMinutes(
      userId,
      dateStr,
      this.getDateInUTC6(startTime),
      this.getDateInUTC6(endTime),
      minutes,
    );

    // check if today qualifies
    const {
      userActivityStreakMinutesPerDay,
      userActivityStreakConsecutiveDays,
      userActivityStreakRewardPoints,
    } = userActivityStreakConfig;
    if (daily.activityDuration < userActivityStreakMinutesPerDay) return;

    // update / reset streak
    const streakDoc = await this.streakRepo.getStreak(userId, true);
    const today = this.formatDateToUTC6();
    const yesterday = this.formatDateToUTC6(new Date(Date.now() - 86_400_000));

    if (
      streakDoc &&
      this.formatDateToUTC6(streakDoc.lastActivityDate) === today
    )
      return;

    if (
      streakDoc &&
      this.formatDateToUTC6(streakDoc.lastActivityDate) === yesterday
    ) {
      if (streakDoc.currentStreak + 1 >= userActivityStreakConsecutiveDays) {
        await this.streakRepo.updateStreak(userId, {
          userId,
          currentStreak: streakDoc.currentStreak + 1,
          lastActivityDate: this.getDateInUTC6(), // UTC+6
          streakStartDate: streakDoc.streakStartDate,
          isCompleted: true,
          isCurrentStreak: false,
        });
        // await this.userPointsProvider.updatePoints(userId, {
        //   type: IUserPointsTransTypeEnum.EARNED,
        //   points: userActivityStreakRewardPoints,
        // });
        await this.userPointsProvider.updatePoints(userId, {
          type: IUserPointsTransTypeEnum.EARNED,
          pointSourceType: IUserPointsSources.DailySteaks,
          // pointSourceId: task.id,
          pointSourceName: 'Daily Streak',
          points: userActivityStreakRewardPoints,
        });
      } else {
        await this.streakRepo.updateStreak(userId, {
          userId,
          currentStreak: streakDoc.currentStreak + 1,
          lastActivityDate: this.getDateInUTC6(), // UTC+6
          streakStartDate: streakDoc.streakStartDate,
          isCompleted: streakDoc.isCompleted,
          isCurrentStreak: true,
        });
      }
    } else {
      await this.streakRepo.updateStreak(userId, {
        userId,
        currentStreak: streakDoc?.currentStreak ?? 0,
        lastActivityDate: streakDoc?.lastActivityDate,
        streakStartDate: streakDoc?.streakStartDate,
        isCompleted: streakDoc?.isCompleted ?? false,
        isCurrentStreak: false,
      });

      await this.streakRepo.create({
        userId,
        currentStreak: 1,
        lastActivityDate: this.getDateInUTC6(), // UTC+6
        streakStartDate: this.getDateInUTC6(), // UTC+6
        isCompleted: false,
        isCurrentStreak: true,
      });
    }
  }

  async getWeeklyStreakInfo(
    userId: string,
  ): Promise<GetUserWeeklyStreakSuccessResponseDto> {
    const todayStreak = await this.checkTodayStreak(userId);

    // Get current date and calculate the start (Sunday) and end (Saturday) of the current week
    const today = new Date();
    const currentWeekStart = new Date(today);
    currentWeekStart.setDate(today.getDate() - today.getDay()); // Go to Sunday
    currentWeekStart.setHours(0, 0, 0, 0);

    const currentWeekEnd = new Date(today);
    currentWeekEnd.setDate(today.getDate() + (6 - today.getDay())); // Go to Saturday
    currentWeekEnd.setHours(23, 59, 59, 999);

    // Get user streak data from DB for the current week
    const userStreakWeek = await this.dailyRepo.getActivityStreakWithTimeline(
      userId,
      currentWeekStart,
      today,
    );

    // Create a map of existing dates for quick lookup
    const existingDates = new Map<string, boolean>();
    userStreakWeek?.forEach((doc) => {
      const dateStr = new Date(doc.date).toISOString().split('T')[0];
      existingDates.set(dateStr, doc.activityMetRequirement);
    });

    // Initialize streak history with existing data
    const streakHistory = [];

    // Iterate through each day of the current week
    const currentDate = new Date(currentWeekStart);
    while (currentDate <= currentWeekEnd) {
      const dateStr = currentDate.toISOString().split('T')[0];
      const isToday =
        currentDate.toISOString().split('T')[0] ===
        today.toISOString().split('T')[0];

      // For today, use the todayStreak value
      if (isToday) {
        streakHistory.push({
          date: new Date(dateStr),
          isStreak: todayStreak,
        });
      }
      // For past dates in the current week
      else if (currentDate < today) {
        streakHistory.push({
          date: new Date(dateStr),
          isStreak: existingDates.get(dateStr) || false,
        });
      }
      // For future dates in the current week
      else {
        streakHistory.push({
          date: new Date(dateStr),
          isStreak: false,
        });
      }

      // Move to next day
      currentDate.setDate(currentDate.getDate() + 1);
    }

    const streakDoc = await this.streakRepo.getStreak(userId, false);
    const response: UserWeeklyStreakResponse = {
      userId,
      currentStreak: streakDoc?.currentStreak ?? 0,
      streakHistory,
    };
    return this.helper.serviceResponse.successResponse(response);
  }

  async getMonthlyStreakInfo(
    userId: string,
    monthsBack = 0,
  ): Promise<GetUserMonthlyStreakSuccessResponseDto> {
    // Get current date and calculate the start and end of the target month
    const today = new Date();
    const targetMonth = new Date(today);
    targetMonth.setMonth(today.getMonth() - monthsBack);

    // Set to first day of the target month at UTC midnight
    const monthStart = new Date(
      Date.UTC(
        targetMonth.getFullYear(),
        targetMonth.getMonth(),
        1,
        0,
        0,
        0,
        0,
      ),
    );

    // Set to last day of the target month at UTC end of day
    const monthEnd = new Date(
      Date.UTC(
        targetMonth.getFullYear(),
        targetMonth.getMonth() + 1,
        0,
        23,
        59,
        59,
        999,
      ),
    );

    // Get user's streak data for the month
    const userStreakMonth = await this.dailyRepo.getActivityStreakWithTimeline(
      userId,
      monthStart,
      monthEnd,
    );

    // Create a map of existing dates for quick lookup
    const existingDates = new Map<string, boolean>();
    userStreakMonth?.forEach((doc) => {
      // Convert to UTC date string to avoid timezone issues
      const utcDate = new Date(doc.date);
      const dateStr = utcDate.toISOString().split('T')[0];
      existingDates.set(dateStr, doc.activityMetRequirement);
    });

    // Initialize streak history
    const streakHistory = [];

    // Iterate through each day of the month
    const currentDate = new Date(monthStart);
    while (currentDate <= monthEnd) {
      // Get UTC date string
      const dateStr = currentDate.toISOString().split('T')[0];

      // For past dates in the current month
      if (currentDate <= today) {
        streakHistory.push({
          date: new Date(dateStr + 'T00:00:00.000Z'), // Ensure UTC midnight
          isStreak: existingDates.get(dateStr) || false,
        });
      }
      // For future dates in the current month
      else {
        streakHistory.push({
          date: new Date(dateStr + 'T00:00:00.000Z'), // Ensure UTC midnight
          isStreak: false,
        });
      }

      // Move to next day (add 24 hours in milliseconds)
      currentDate.setTime(currentDate.getTime() + 24 * 60 * 60 * 1000);
    }

    const response: GetUserMonthlyStreakResponseDtoForUser = {
      userId,
      streakHistory,
    };
    return this.helper.serviceResponse.successResponse(response);
  }

  // TODO: what should be input parameters? from (date) and to (date)
  async getUserStreakHistory(
    userId: string,
    offset: number,
    limit: number,
  ): Promise<UserActivityStreakHistoryResponse> {
    const activities = await this.dailyRepo.getUserActivityHistory(
      userId,
      offset,
      limit,
    );
    const streakHistory = activities.map((doc) => ({
      date: new Date(doc.date),
      isStreak: doc.activityMetRequirement,
    }));
    const response: UserActivityStreakHistoryResponse = {
      userId,
      streakHistory,
    };
    return response;
  }
}
