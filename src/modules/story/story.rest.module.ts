import { Modu<PERSON> } from '@nestjs/common';
import { StoryRepository } from './repository';
import { StoryHelperRepository } from './repository/story.helper.repository';
import { StoryController } from './rest';
import { StoryService } from './services';
import { UserRepository } from '../user/repositories';
import { NotificationModule } from '../notification/notification.module';

@Module({
  imports: [NotificationModule],
  controllers: [StoryController],
  providers: [
    StoryService,
    StoryRepository,
    StoryHelperRepository,
    UserRepository,
  ],
})
export class StoryModule {}
