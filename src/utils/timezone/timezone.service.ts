import { Injectable } from '@nestjs/common';
import * as moment from 'moment-timezone';

export interface TimezoneConfig {
  serverTimezone: string;
  defaultClientTimezone: string;
}

@Injectable()
export class TimezoneService {
  private readonly config: TimezoneConfig;

  constructor() {
    this.config = {
      serverTimezone: process.env.SERVER_TIMEZONE || 'UTC',
      defaultClientTimezone: process.env.DEFAULT_CLIENT_TIMEZONE || 'Asia/Dhaka', // UTC+6
    };
  }

  /**
   * Get current UTC timestamp
   * Always use this for database operations and internal calculations
   */
  getCurrentUTCTimestamp(): number {
    return moment.utc().valueOf();
  }

  /**
   * Get current UTC Date object
   */
  getCurrentUTCDate(): Date {
    return moment.utc().toDate();
  }

  /**
   * Get current time in server timezone
   */
  getCurrentServerTime(): moment.Moment {
    return moment.tz(this.config.serverTimezone);
  }

  /**
   * Get current time in client timezone
   */
  getCurrentClientTime(timezone?: string): moment.Moment {
    const tz = timezone || this.config.defaultClientTimezone;
    return moment.tz(tz);
  }

  /**
   * Convert UTC timestamp to specific timezone
   */
  convertUTCToTimezone(utcTimestamp: number, timezone?: string): moment.Moment {
    const tz = timezone || this.config.defaultClientTimezone;
    return moment.utc(utcTimestamp).tz(tz);
  }

  /**
   * Convert timezone-specific time to UTC timestamp
   */
  convertTimezoneToUTC(dateTime: string | Date | moment.Moment, timezone?: string): number {
    const tz = timezone || this.config.defaultClientTimezone;
    return moment.tz(dateTime, tz).utc().valueOf();
  }

  /**
   * Add time to current UTC timestamp
   * @param amount - amount to add
   * @param unit - time unit (milliseconds, seconds, minutes, hours, days, etc.)
   */
  addTimeToUTC(amount: number, unit: moment.unitOfTime.DurationConstructor): number {
    return moment.utc().add(amount, unit).valueOf();
  }

  /**
   * Add time to specific UTC timestamp
   */
  addTimeToUTCTimestamp(
    utcTimestamp: number,
    amount: number,
    unit: moment.unitOfTime.DurationConstructor
  ): number {
    return moment.utc(utcTimestamp).add(amount, unit).valueOf();
  }

  /**
   * Check if UTC timestamp is expired
   */
  isUTCTimestampExpired(utcTimestamp: number): boolean {
    return moment.utc().valueOf() > utcTimestamp;
  }

  /**
   * Get difference between two UTC timestamps in specified unit
   */
  getUTCTimestampDifference(
    timestamp1: number,
    timestamp2: number,
    unit: moment.unitOfTime.Diff = 'milliseconds'
  ): number {
    return moment.utc(timestamp1).diff(moment.utc(timestamp2), unit);
  }

  /**
   * Format UTC timestamp for display in specific timezone
   */
  formatUTCForDisplay(
    utcTimestamp: number,
    format: string = 'YYYY-MM-DD HH:mm:ss',
    timezone?: string
  ): string {
    const tz = timezone || this.config.defaultClientTimezone;
    return moment.utc(utcTimestamp).tz(tz).format(format);
  }

  /**
   * Get start of day in UTC for a specific timezone
   * Useful for date range queries
   */
  getStartOfDayUTC(timezone?: string): number {
    const tz = timezone || this.config.defaultClientTimezone;
    return moment.tz(tz).startOf('day').utc().valueOf();
  }

  /**
   * Get end of day in UTC for a specific timezone
   */
  getEndOfDayUTC(timezone?: string): number {
    const tz = timezone || this.config.defaultClientTimezone;
    return moment.tz(tz).endOf('day').utc().valueOf();
  }

  /**
   * Get start and end of day in UTC for date range queries
   */
  getDayRangeUTC(date?: string | Date, timezone?: string): { start: number; end: number } {
    const tz = timezone || this.config.defaultClientTimezone;
    const targetDate = date ? moment.tz(date, tz) : moment.tz(tz);
    
    return {
      start: targetDate.startOf('day').utc().valueOf(),
      end: targetDate.endOf('day').utc().valueOf(),
    };
  }

  /**
   * Get date range for N days from now in UTC
   * Useful for scheduling and expiration queries
   */
  getDateRangeFromNow(
    days: number,
    timezone?: string
  ): { start: number; end: number } {
    const tz = timezone || this.config.defaultClientTimezone;
    const targetDate = moment.tz(tz).add(days, 'days');
    
    return {
      start: targetDate.startOf('day').utc().valueOf(),
      end: targetDate.endOf('day').utc().valueOf(),
    };
  }

  /**
   * Create OTP expiration timestamp
   * @param expirationMinutes - expiration time in minutes
   */
  createOTPExpirationTimestamp(expirationMinutes: number): number {
    return this.addTimeToUTC(expirationMinutes, 'minutes');
  }

  /**
   * Validate if OTP is still valid
   */
  isOTPValid(otpExpirationTimestamp: number): boolean {
    return !this.isUTCTimestampExpired(otpExpirationTimestamp);
  }

  /**
   * Get cron expression adjusted for timezone
   * Converts local time cron to UTC cron
   */
  convertCronToUTC(
    cronExpression: string,
    fromTimezone?: string
  ): string {
    // This is a simplified version - for complex cron expressions,
    // you might want to use a more sophisticated library
    const tz = fromTimezone || this.config.defaultClientTimezone;
    
    // Parse basic cron format: minute hour day month dayOfWeek
    const parts = cronExpression.split(' ');
    if (parts.length !== 5) {
      return cronExpression; // Return as-is if not standard format
    }

    const [minute, hour, day, month, dayOfWeek] = parts;
    
    // Convert hour to UTC
    if (hour !== '*' && !isNaN(parseInt(hour))) {
      const localTime = moment.tz(`2023-01-01 ${hour}:${minute || '0'}`, tz);
      const utcTime = localTime.utc();
      
      return `${utcTime.minute()} ${utcTime.hour()} ${day} ${month} ${dayOfWeek}`;
    }
    
    return cronExpression;
  }

  /**
   * Get timezone offset in minutes
   */
  getTimezoneOffset(timezone?: string): number {
    const tz = timezone || this.config.defaultClientTimezone;
    return moment.tz(tz).utcOffset();
  }

  /**
   * Get available timezone list
   */
  getAvailableTimezones(): string[] {
    return moment.tz.names();
  }

  /**
   * Validate timezone string
   */
  isValidTimezone(timezone: string): boolean {
    return moment.tz.names().includes(timezone);
  }
}
