export class UserActivityStreak {
	userId: string;
	currentStreak: number;        // Current consecutive days
	lastActivityDate: Date;       // Last activity date
	streakStartDate: Date;        // When current streak started
	isCompleted: boolean;         // Whether the streak is completed
	isCurrentStreak: boolean;     // Whether the streak is current
}

export class StreakInfo {
	date: Date;
	isStreak: boolean;
}

export class UserWeeklyStreakResponse {
	userId: string;
	currentStreak: number;
	streakHistory: StreakInfo[]
}

export class UserActivityStreakHistoryResponse {
	userId: string;
	streakHistory: StreakInfo[]
}