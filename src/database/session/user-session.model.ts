import { randomUUID } from 'crypto';
import { model, Schema } from 'mongoose';
import { UserSession } from 'src/entity/user-session';

/**
 * User Session Schema with timezone-aware timestamp handling
 *
 * Timezone Convention:
 * - expireAt: Date object stored in UTC - when session expires
 * - createdAt/updatedAt: Mongoose automatic timestamps (Date) - stored in UTC
 */
const UserSessionSchema = new Schema<UserSession>(
  {
    sessionId: {
      type: String,
      default: () => randomUUID(),
      unique: true,
    },
    userId: {
      type: String,
      index: true,
    },
    expireAt: {
      type: Date,
    },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

const UserSessionModel = model<UserSession>('user-session', UserSessionSchema);
export { UserSessionModel };
