import { randomUUID } from 'crypto';
import { model, Schema } from 'mongoose';
import { chatbotSession, Message, Role } from 'src/entity/chatbot';

const MessageSchema = new Schema<Message>({
  role: {
    type: String,
    enum: Role,
    default: Role.USER,
    required: true,
  },
  content: {
    type: String,
    required: true,
  },
  createdAt: {
    type: Date, // Date object stored in UTC
    default: () => new Date(), // Use function to get current UTC date
  },
});

const chatbotSchema = new Schema<chatbotSession>(
  {
    id: {
      type: String,
      default: () => randomUUID(),
      unique: true,
    },
    userId: {
      type: String,
    },
    messages: [MessageSchema],
  },
  {
    versionKey: false,
    timestamps: true,
  },
);

const ChatbotModel = model<chatbotSession>('chatbot', chatbotSchema);
export { ChatbotModel };
