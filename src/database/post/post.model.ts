import { randomUUID } from 'crypto';
import { model, Schema } from 'mongoose';
import { Post, PostPrivacy, PostType, PostReactionsType } from 'src/entity/post';
export const PostMediaType = {
  type: [
    {
      url: { type: String, trim: true },
    },
  ],
  default: [],
  _id: false,
};

const PostSchema = new Schema<Post>(
  {
    id: {
      type: String,
      default: () => randomUUID(),
      unique: true,
    },
    userId: {
      type: String,
      index: true,
      required: true,
    },
    type: {
      type: String,
      enum: PostType,
      default: PostType.OWN,
    },
    content: {
      type: String,
      trim: true,
      default: null,
    },
    images: PostMediaType,
    videos: PostMediaType,
    privacy: {
      type: String,
      enum: PostPrivacy,
      index: true,
    },
    totalLikes: {
      type: Number,
      default: 0,
    },
    totalReactions: {
      type: Number,
      default: 0,
    },
    totalComments: {
      type: Number,
      default: 0,
    },
    totalShares: {
      type: Number,
      default: 0,
    },
    reactions: {
      type: Map,
      of: Number,
      // default: () => ({ [PostReactionsType.LIKE]: 0 }) 
      default: {}
    }, 
    location: {
      type: String,
      default: null,
    },
    weight: {
      // like = 5, comment = 10, share = 15
      type: Number,
      default: 0,
      select: false,
      index: true,
    },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);


PostSchema.add({ sharedPost: PostSchema });
const PostModel = model<Post>('post', PostSchema);
export { PostModel };
