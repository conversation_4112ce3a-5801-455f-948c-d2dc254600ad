import { model, Schema } from 'mongoose';
import { Otp } from 'src/entity/otp';

/**
 * OTP Schema with timezone-aware timestamp handling
 *
 * Timezone Convention:
 * - otpExpireTime: UTC timestamp (Number) - when OTP expires
 * - otpVerifiedAt: UTC timestamp (Number) - when <PERSON><PERSON> was verified
 * - createdAt/updatedAt: Mongoose automatic timestamps (Date) - stored in UTC
 */
const OtpSchema = new Schema<Otp>(
  {
    email: String,
    otp: Number,
    isVerified: {
      type: Boolean,
      default: false,
    },
    otpExpireTime: Number, // UTC timestamp for timezone consistency
    otpVerifiedAt: Number, // UTC timestamp for timezone consistency
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

const OtpModel = model<Otp>('otp', OtpSchema);
export { OtpModel };
