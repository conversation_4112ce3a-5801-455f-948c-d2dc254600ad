import { Injectable } from '@nestjs/common';
import {
  BatchResponse,
  MessagingTopicResponse,
} from 'firebase-admin/messaging';
import {
  IFcmService,
  IndividualFcmRequest,
  PushPriority,
} from './fcm.service.interface';
import * as admin from 'firebase-admin';
import { fireBaseConfig } from 'config/fcm';

@Injectable()
export class FcmService implements IFcmService {
  constructor() {
    !admin.apps.length &&
      admin.initializeApp({
        credential: admin.credential.cert(
          JSON.parse(JSON.stringify(fireBaseConfig)),
        ),
      });
  }

  async sendToIndividual({
    token,
    title,
    body,
    documentId,
    data,
    isHighPriority,
    ttl,
  }: IndividualFcmRequest): Promise<boolean> {
    try {
      const payload = {
        notification: {
          title,
          body,
        },
        data: {
          ...data,
          documentId,
          title,
          body,
        },
        android: {
          // notification: {
          //   title,
          //   body,
          // },
          // Priority of the message. Must be either `normal` or `high`.
          priority: isHighPriority ? PushPriority.HIGH : PushPriority.NORMAL,
          collapseKey: '',
          ttl, // Time-to-live duration of the message in seconds.
        },
        apns: {
          headers: {
            'apns-expiration': ((new Date().getTime() + ttl) / 1000).toFixed(),
            'apns-priority': isHighPriority ? '10' : '5',
          },
        },
        token,
      };
      await admin.messaging().send(payload);
      return true;
    } catch (error) {
      console.error('FCM sendToIndividual error:', error?.message || error);
      return false;
    }
  }

  async sendToMany(
    tokens: string[],
    title: string,
    body: string,
    documentId: string,
    data: object,
    isHighPriority: boolean,
    ttl: number,
  ): Promise<boolean> {
    try {
      const payload = {
        notification: {
          title,
          body,
        },
        data: {
          ...data,
          documentId,
        },
        android: {
          notification: {
            title,
            body,
          },
          // Priority of the message. Must be either `normal` or `high`.
          priority: isHighPriority ? PushPriority.HIGH : PushPriority.NORMAL,
          collapseKey: '',
          ttl, // Time-to-live duration of the message in seconds.
        },
        apns: {
          headers: {
            'apns-expiration': ((new Date().getTime() + ttl) / 1000).toFixed(),
            'apns-priority': isHighPriority ? '10' : '5',
          },
        },
        tokens,
      };
      await admin.messaging().sendMulticast(payload);
      return true;
    } catch (error) {
      console.error('FCM sendToMany error:', error?.message || error);
      return false;
    }
  }

  async sendToTopic({
    title,
    body,
    documentId,
    data,
    topic,
  }): Promise<MessagingTopicResponse> {
    try {
      const payload = {
        notification: {
          title,
          body,
        },
        data: {
          ...data,
          documentId,
        },
        topic,
      };
      await admin.messaging().send(payload);
    } catch (error) {
      console.error('FCM sendToTopic error:', error?.message || error);
      return;
    }
  }

  async subscribeNotificationTopic(tokens: string | string[], topic: string) {
    try {
      await admin.messaging().subscribeToTopic(tokens, topic);
      // Subscription successful
    } catch (error) {
      console.error('FCM subscribeNotificationTopic error:', error?.message || error);
      return;
    }
  }

  async unsubscribeNotificationTopic(tokens: string | string[], topic: string) {
    try {
      await admin
        .messaging()
        .unsubscribeFromTopic(tokens, topic);
      // Unsubscription successful
    } catch (error) {
      console.error('FCM unsubscribeNotificationTopic error:', error?.message || error);
      return;
    }
  }
}
