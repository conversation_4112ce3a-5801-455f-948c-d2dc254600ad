import { BlobSASPermissions, BlobServiceClient, BlockBlobClient, generateBlobSASQueryParameters, StorageSharedKeyCredential } from '@azure/storage-blob';
import { Injectable } from '@nestjs/common';
import { azureConfig } from 'config/azure';
import { randomInt } from 'crypto';
import { IAzureBlobStorageService } from './azureBlobStorage.service.interface';

@Injectable()
export class AzureBlobStorageService implements IAzureBlobStorageService {
  private blobServiceClient: BlobServiceClient;
  private containerName: string;
  private publicContainerName: string;
  private presignedExpireTime: number;
  private sharedKeyCredential: StorageSharedKeyCredential;

  constructor() {
    const {
      azure_connection_string,
      azure_container_name,
      azure_public_container_name,
      azure_presigned_expire_time,
      azure_storage_account,
      azure_storage_key,
    } = azureConfig;

    this.containerName = azure_container_name;
    this.publicContainerName = azure_public_container_name;
    this.presignedExpireTime = azure_presigned_expire_time;
    this.sharedKeyCredential = new StorageSharedKeyCredential(
      azure_storage_account,
      azure_storage_key
    );
    this.blobServiceClient = BlobServiceClient.fromConnectionString(azure_connection_string);
  }

  public getBlockBlobClient(containerName: string, blobName: string): BlockBlobClient {
    const containerClient = this.blobServiceClient.getContainerClient(containerName);
    return containerClient.getBlockBlobClient(blobName);
  }

  generateFileKey(
    userId: string,
    fileName: string,
    featureName?: string,
  ): string {
    // key = feature-name/year/month/day/user-id/random_current-timestamp
    fileName = fileName?.replace(/[^a-zA-Z0-9.]/g, '');
    const year = new Date().getFullYear();
    const month = new Date().getMonth() + 1;
    const date = new Date().getDate();
    const random = randomInt(100000, 999999);
    const key = `original/${
      featureName ? featureName : 'feature'
    }/${year}/${month}/${date}/${userId}/${random}_${Date.now()}${fileName}`;
    return key;
  }

  getFileKey(url: string, featureName: string) {
    return url.substring(url.lastIndexOf(featureName), url.length);
  }

  async generatePreSignedUrl(
    url: string,
    featureName: string,
    fileName?: string,
    type?: string,
  ): Promise<string | null> {
    try {
      if (type === 'putObject') {

        const key = url ? this.getFileKey(url, featureName) : fileName;
        if (!key) return null;

        // Determine which container to use based on the operation type
        const containerName = this.containerName;
        const blobClient = this.getBlockBlobClient(containerName, key);

        const startsOn = new Date();
        const expiresOn = new Date(startsOn.valueOf() + this.presignedExpireTime * 1000);

        // Create the appropriate permissions object
        const permissions = new BlobSASPermissions();
        permissions.create = true;
        permissions.write = true;

        const sasToken = generateBlobSASQueryParameters({
          containerName,
          blobName: key,
          permissions,
          startsOn,
          expiresOn,
        }, this.sharedKeyCredential).toString();
        return `${blobClient.url}?${sasToken}`;
      }
      else {
        return this.generateCdnPresignedUrl(url, featureName, fileName);
      }
    } catch (error) {
      console.log(error.message);
      return null;
    }
  }

  async uploadToAzureBlob(
    file: Express.Multer.File,
    key: string,
    isPublic = false,
  ): Promise<string | null> {
    try {
      const containerName = isPublic ? this.publicContainerName : this.containerName;
      const blockBlobClient = this.getBlockBlobClient(containerName, key);

      await blockBlobClient.uploadData(file.buffer, {
        blobHTTPHeaders: {
          blobContentType: file.mimetype,
        }
      });

      // Return the URL of the uploaded blob
      const url = isPublic 
        ? `${azureConfig.azure_pub_url}/${key}`
        : blockBlobClient.url;
      return url;
    } catch (error) {
      console.log('Error in upload to Azure Blob Storage: ', error);
      return null;
    }
  }

  async generateCdnPresignedUrl(
    url: string,
    featureName: string,
    fileName?: string,
  ) {
    try {
      const key = url ? this.getFileKey(url, featureName) : fileName;
      if (!key) return null;

      const startsOn = new Date();
      const expiresOn = new Date(startsOn.valueOf() + this.presignedExpireTime * 1000);

      // Create the appropriate permissions object
      const permissions = new BlobSASPermissions();
      permissions.read = true;

      const sasToken = generateBlobSASQueryParameters({
        containerName: this.containerName,
        blobName: key,
        permissions,
        startsOn,
        expiresOn,
      }, this.sharedKeyCredential).toString();

      const cdnUrl = `${azureConfig.azure_cdn_baseurl}/${key}?${sasToken}`;
      return cdnUrl;
    } catch (error) {
      console.log(error);
      return null;
    }
  }
}
